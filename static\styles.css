/* متغيرات CSS العامة للألوان والقيم */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --text-color: #2c3e50;
    --light-text: #7f8c8d;
    --bg-color: #ecf0f1;
    --light-bg: #ffffff;
    --border-color: #bdc3c7;
    --border-radius: 12px;
    --box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعادة تعيين الهوامش والحشو لجميع العناصر */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* تنسيق الجسم الرئيسي للصفحة */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--text-color);
    line-height: 1.7;
    min-height: 100vh;
}


/* تنسيق رأس الصفحة (Header) */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* text-align: center; */
    padding: 15px 40px;
    background: var(--light-bg);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

/* حاوي الشعار */
.logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* صورة الشعار */
.logo-img {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    object-fit: cover;
    border: 3px solid var(--secondary-color);
    transition: all 0.4s ease;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
    cursor: pointer;
}

/* تأثير عند تمرير الماوس على الشعار */
.logo-img:hover {
    transform: scale(1.15) rotate(5deg);
    border-color: var(--accent-color);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
    filter: brightness(1.1);
}

/* نص الشعار */
.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
    letter-spacing: 1px;
}

/* تأثير عند تمرير الماوس على نص الشعار */
.logo-text:hover {
    color: var(--secondary-color);
    transform: translateY(-2px);
}

/* تنسيق شريط التنقل */
/* قائمة التنقل */
nav ul {
    display: flex;
    list-style: none;
    gap: 8px;
    align-items: center;
}

/* روابط التنقل */
nav ul li a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    padding: 10px 18px;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    background: rgba(255,255,255,0.1);
    border: 1px solid transparent;
}

/* تأثير الإضاءة على روابط التنقل */
nav ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: var(--transition);
}

nav ul li a:hover::before {
    left: 100%;
}

nav ul li a:hover, nav ul li a.active {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
    border-color: var(--secondary-color);
}


/* المحتوى الرئيسي */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* قسم البطل الرئيسي */
.hero {
    text-align: center;
    padding: 80px 40px;
    margin-bottom: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.hero::before {
    content: "";
    position: absolute;
    top: -100px;
    left: -100px;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.hero::after {
    content: "";
    position: absolute;
    bottom: -80px;
    right: -80px;
    width: 160px;
    height: 160px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero h1 {
    font-size: 48px;
    margin-bottom: 20px;
    color: white;
    position: relative;
    z-index: 2;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero p {
    font-size: 22px;
    margin-bottom: 35px;
    color: rgba(255,255,255,0.9);
    position: relative;
    z-index: 2;
    font-weight: 300;
}


/* تنسيق الأزرار */
.btn {
    display: inline-block;
    padding: 14px 30px;
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    color: white;
    text-decoration: none;
    border-radius: 30px;
    font-weight: 600;
    font-size: 16px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 2;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    transition: var(--transition);
    z-index: -1;
}

.btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.btn:hover::before {
    left: 100%;
}

.btn:active {
    transform: translateY(-1px) scale(1.02);
}


/* قسم المنتجات */
.featured-products h2, .products-section h2 {
    text-align: center;
    margin-bottom: 50px;
    color: var(--text-color);
    font-size: 36px;
    font-weight: 700;
    position: relative;
    padding-bottom: 20px;
}

.featured-products h2::after, .products-section h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    border-radius: 2px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    padding: 20px 0;
}

.product-card {
    background: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.2);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    transform: scaleX(0);
    transition: var(--transition);
}

.product-card:hover::before {
    transform: scaleX(1);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 20px;
    transition: var(--transition);
}

.product-card:hover img {
    transform: scale(1.08);
}

.product-card h3 {
    margin-bottom: 15px;
    color: var(--text-color);
    font-size: 22px;
    font-weight: 600;
}

.price {
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 20px;
    font-size: 20px;
}


/* تذييل الصفحة */
footer {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
    color: white;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color), var(--success-color));
}

footer p {
    font-size: 16px;
    font-weight: 300;
    opacity: 0.9;
}


.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background-color: var(--primary-color);
    color: white;
    padding: 15px 25px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.5s ease, transform 0.3s ease;
    transform: translateY(0);
}

.notification.hide {
    opacity: 0;
    transform: translateY(-20px);
}

.cart-count {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    font-size: 12px;
    font-weight: bold;
    width: 22px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    border-radius: 50%;
    margin-right: 5px;
    position: relative;
    top: -10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
}


/* تنسيق النماذج */
.form-container {
    max-width: 480px;
    margin: 60px auto;
    padding: 40px;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
}

.form-container h2 {
    text-align: center;
    margin-bottom: 35px;
    color: var(--text-color);
    font-size: 32px;
    font-weight: 700;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: 16px;
    background: rgba(255,255,255,0.8);
    font-family: inherit;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    background: white;
    transform: translateY(-2px);
}

input:invalid {
    border-color: var(--accent-color);
}

/* تنسيق الجداول */
.table-container {
    background: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    overflow-x: auto;
    margin: 20px 0;
}

.products-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: 16px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.products-table thead tr {
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    color: white;
    text-align: center;
}

.products-table th,
.products-table td {
    padding: 18px 15px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.products-table th {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

.products-table tbody tr {
    background: white;
    transition: var(--transition);
}

.products-table tbody tr:hover {
    background: rgba(52, 152, 219, 0.05);
    transform: scale(1.01);
}

.products-table tbody tr:nth-child(even) {
    background: rgba(0,0,0,0.02);
}

.product-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.product-img:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.price-tag {
    background: linear-gradient(45deg, var(--accent-color), #c0392b);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
    display: inline-block;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 40px 0;
}

.empty-state h2 {
    color: var(--light-text);
    font-size: 28px;
    margin-bottom: 15px;
}

.empty-state p {
    color: var(--light-text);
    font-size: 16px;
}


.checkout-btn, 
.place-order-btn {
    display: block;
    width: 100%;
    padding: 15px;
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    text-decoration: none;
    border-radius: var(--border-radius);
    margin-top: 25px;
    font-weight: bold;
    font-size: 18px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.checkout-btn:hover, 
.place-order-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.checkout-btn:active, 
.place-order-btn:active {
    transform: translateY(0);
}


.cart-container {
    max-width: 1000px;
    margin: 50px auto;
    padding: 35px;
    background-color: var(--light-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.cart-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 35px;
}

.cart-table th {
    background-color: #f1f1f1;
    padding: 15px;
    text-align: right;
    border-bottom: 2px solid #ddd;
    font-weight: 600;
}

.cart-table td {
    padding: 20px 15px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.cart-table img {
    width: 90px;
    height: 90px;
    object-fit: cover;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.cart-table img:hover {
    transform: scale(1.1);
}

.quantity-input {
    width: 70px;
    padding: 8px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.remove-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.remove-btn:hover {
    background-color: #c0392b;
    transform: scale(1.05);
}


/* Responsive Design */
@media (max-width: 768px) {
    header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }

    .logo-container {
        gap: 8px;
    }

    .logo-img {
        width: 40px;
        height: 40px;
        border-radius: 10px;
    }

    .logo-text {
        font-size: 20px;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }

    nav ul li a {
        padding: 8px 12px;
        font-size: 14px;
    }

    .hero {
        padding: 50px 20px;
        margin-bottom: 40px;
    }

    .hero h1 {
        font-size: 32px;
    }

    .hero p {
        font-size: 18px;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .form-container {
        margin: 30px 20px;
        padding: 25px;
    }

    .table-container {
        padding: 15px;
        overflow-x: auto;
    }

    .products-table {
        font-size: 14px;
    }

    .products-table th,
    .products-table td {
        padding: 12px 8px;
    }

    .cart-container {
        padding: 15px;
    }

    .cart-table th,
    .cart-table td {
        padding: 10px 5px;
        font-size: 14px;
    }

    .cart-actions {
        flex-direction: column;
        gap: 15px;
    }

    .cart-actions .btn {
        width: 100%;
        text-align: center;
    }

    .product-img-small {
        width: 40px;
        height: 40px;
    }

    .product-img {
        width: 60px;
        height: 60px;
    }

    main {
        padding: 20px 15px;
    }
}

/* تحسين التجاوب مع الهواتف المحمولة */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    header {
        padding: 10px 15px;
        flex-direction: column;
        gap: 15px;
    }

    .logo-container {
        order: 1;
    }

    .logo-text {
        font-size: 16px;
    }

    nav {
        order: 2;
        width: 100%;
    }

    nav ul {
        flex-direction: column;
        gap: 5px;
    }

    nav ul li a {
        padding: 12px;
        text-align: center;
        border-radius: var(--border-radius);
        background: var(--bg-color);
        font-size: 12px;
    }

    .hero h1, .hero-section h1 {
        font-size: 24px;
    }

    .hero p, .hero-section p {
        font-size: 16px;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-container {
        padding: 20px;
        margin: 10px;
    }
}


@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.team-member {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
}

.team-member:nth-child(1) { animation-delay: 0.1s; }
.team-member:nth-child(2) { animation-delay: 0.3s; }
.team-member:nth-child(3) { animation-delay: 0.5s; }
.team-member:nth-child(4) { animation-delay: 0.7s; }


.btn.added {
    background-color: #9b0626;
    pointer-events: none;
}


@keyframes cartShake {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}

.cart-shake {
    animation: cartShake 0.5s ease;
}


.mission-section {
    position: relative;
    overflow: hidden;
}

.mission-section::before {
    content: "";
    position: absolute;
    top: -30px;
    right: -30px;
    width: 60px;
    height: 60px;
    background-color: rgba(52, 152, 219, 0.1);
    border-radius: 50%;
}

.mission-section::after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: -20px;
    width: 40px;
    height: 40px;
    background-color: rgba(52, 152, 219, 0.1);
    border-radius: 50%;
}


.social-links a {
    position: relative;
    overflow: hidden;
}

.social-links a::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.social-links a:hover::before {
    transform: translateX(100%);
}

/* Additional Enhancements */
.page-title {
    text-align: center;
    margin: 40px 0;
    color: var(--text-color);
    font-size: 36px;
    font-weight: 700;
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    border-radius: 2px;
}

/* أنيميشن التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* حركة اللوجو الجديدة - نبضة وتوهج */
@keyframes logoGlow {
    0% {
        transform: scale(1) rotate(0deg);
        filter: brightness(1) drop-shadow(0 0 0 rgba(74, 144, 226, 0));
        border-color: var(--secondary-color);
    }
    25% {
        transform: scale(1.2) rotate(5deg);
        filter: brightness(1.3) drop-shadow(0 0 15px rgba(74, 144, 226, 0.6));
        border-color: var(--accent-color);
    }
    50% {
        transform: scale(1.25) rotate(-3deg);
        filter: brightness(1.4) drop-shadow(0 0 20px rgba(74, 144, 226, 0.8));
        border-color: #ff6b6b;
    }
    75% {
        transform: scale(1.15) rotate(2deg);
        filter: brightness(1.2) drop-shadow(0 0 12px rgba(74, 144, 226, 0.5));
        border-color: var(--accent-color);
    }
    100% {
        transform: scale(1) rotate(0deg);
        filter: brightness(1) drop-shadow(0 0 0 rgba(74, 144, 226, 0));
        border-color: var(--secondary-color);
    }
}

/* Success/Error Messages */
.message {
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-align: center;
}

.message.success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.message.error {
    background: rgba(231, 76, 60, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.message.info {
    background: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

/* تنسيق صفحة من نحن */
.about-section {
    max-width: 1000px;
    margin: 0 auto;
    padding: 40px 20px;
}

.about-content {
    display: grid;
    gap: 40px;
}

.mission-section,
.vision-section,
.values-section,
.contact-section,
.team-section {
    background: var(--light-bg);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.mission-section h3,
.vision-section h3,
.values-section h3,
.contact-section h3,
.team-section h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 24px;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 10px;
}

.values-section ul {
    list-style: none;
    padding: 0;
}

.values-section li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 16px;
}

.values-section li:last-child {
    border-bottom: none;
}

.contact-info p {
    margin: 15px 0;
    font-size: 16px;
    color: var(--text-color);
}

.form-links {
    text-align: center;
    margin-top: 20px;
}

.form-links a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
}

.form-links a:hover {
    text-decoration: underline;
}





@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}



/* زر العودة للأعلى */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
    font-size: 18px;
}

.scroll-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: var(--primary-color);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Search Bar Enhancement */
.search-container {
    position: relative;
    max-width: 400px;
    margin: 20px auto;
}

.search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--secondary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--secondary-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--primary-color);
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 12px;
    color: white;
}

.badge.new {
    background: var(--success-color);
}

.badge.sale {
    background: var(--accent-color);
}

.badge.featured {
    background: var(--warning-color);
}

/* Breadcrumb */
.breadcrumb {
    padding: 15px 0;
    margin-bottom: 20px;
    background: transparent;
}

.breadcrumb ol {
    list-style: none;
    display: flex;
    align-items: center;
    gap: 10px;
}

.breadcrumb li {
    color: var(--light-text);
}

.breadcrumb li:not(:last-child)::after {
    content: "←";
    margin-right: 10px;
    color: var(--border-color);
}

.breadcrumb a {
    color: var(--secondary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* تنسيق السلة */
.cart-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.empty-cart {
    text-align: center;
    padding: 60px 20px;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.empty-cart p {
    font-size: 18px;
    color: var(--light-text);
    margin-bottom: 20px;
}

.cart-table-container {
    background: var(--light-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

.cart-table {
    width: 100%;
    border-collapse: collapse;
}

.cart-table th,
.cart-table td {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.cart-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.cart-table tbody tr:hover {
    background: #f8f9fa;
}

.cart-table .total-row {
    background: var(--secondary-color);
    color: white;
    font-weight: 600;
}

.cart-table .total-row td {
    border-bottom: none;
}

.btn-remove {
    background: var(--accent-color);
    color: white;
    padding: 8px 15px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
}

.btn-remove:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.cart-actions {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 30px;
}

.btn-secondary {
    background: var(--light-text);
    color: white;
}

.btn-secondary:hover {
    background: #6c757d;
}

.btn-primary {
    background: var(--success-color);
    color: white;
}

.btn-primary:hover {
    background: #219a52;
}

/* تنسيق الجداول العامة */
.table-container {
    background: var(--light-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin: 30px 0;
}

.products-table {
    width: 100%;
    border-collapse: collapse;
}

.products-table th,
.products-table td {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.products-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.products-table tbody tr:hover {
    background: #f8f9fa;
}

.product-img-small {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid var(--border-color);
}

.btn-small {
    padding: 8px 15px;
    font-size: 14px;
    border-radius: 6px;
}

/* تحسينات إضافية للتصميم الموحد */
.notification {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
}

/* تنسيق موحد للعناوين */
h1, h2, h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
    font-weight: 600;
}

h3 {
    font-size: 1.5rem;
    font-weight: 500;
}

/* تنسيق موحد للفوتر */
footer {
    background: var(--primary-color);
    color: white;
    text-align: center;
    padding: 30px 20px;
    margin-top: 60px;
}

footer p {
    margin: 0;
    font-size: 16px;
}

/* تحسينات للتنقل */
nav ul li a.active {
    background: var(--secondary-color);
    color: white;
    font-weight: 600;
}

/* تحسينات للأزرار */
.btn {
    display: inline-block;
    padding: 12px 25px;
    background: var(--secondary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    text-align: center;
}

.btn:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}




